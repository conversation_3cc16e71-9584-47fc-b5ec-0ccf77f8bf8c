{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "shader_studio",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "shader_studio impeller",
            "request": "launch",
            "type": "dart",
            "args": [
                "--enable-impeller"
            ]
        },
        {
            "name": "shader_studio (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
        },
        {
            "name": "shader_studio (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
        }
    ]
}