{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Desktop/shader_studio-main/build/.cxx/Debug/115b5b3g/armeabi-v7a", "source": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}