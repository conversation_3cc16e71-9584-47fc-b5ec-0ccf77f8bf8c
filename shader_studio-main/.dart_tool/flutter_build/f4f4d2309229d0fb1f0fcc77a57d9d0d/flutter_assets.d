 /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/lib/shaders/rgb.frag /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/lib/shaders/debug.frag /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/lib/shaders/blur.frag /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.json /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/FontManifest.json /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NOTICES.Z /Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/Desktop/shader_studio-main/pubspec.yaml /Users/<USER>/Desktop/shader_studio-main/ios/Runner/Info.plist /Users/<USER>/Desktop/shader_studio-main/ios/Flutter/AppFrameworkInfo.plist /Users/<USER>/Desktop/shader_studio-main/lib/shaders/rgb.frag /Users/<USER>/Desktop/shader_studio-main/lib/shaders/debug.frag /Users/<USER>/Desktop/shader_studio-main/lib/shaders/blur.frag /Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/Desktop/shader_studio-main/.dart_tool/flutter_build/f4f4d2309229d0fb1f0fcc77a57d9d0d/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-3.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.0.4/LICENSE /Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE /Users/<USER>/development/flutter/packages/flutter/LICENSE