["/Users/<USER>/Desktop/shader_studio-main/ios/Flutter/ephemeral/flutter_lldbinit", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/Flutter.framework/Flutter", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/vm_snapshot_data", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/kernel_blob.bin", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/App", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/Info.plist", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/lib/shaders/rgb.frag", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/lib/shaders/debug.frag", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/lib/shaders/blur.frag", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NOTICES.Z", "/Users/<USER>/Desktop/shader_studio-main/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NativeAssetsManifest.json"]