["/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/lib/shaders/rgb.frag", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/lib/shaders/debug.frag", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/lib/shaders/blur.frag", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/Desktop/shader_studio-main/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]