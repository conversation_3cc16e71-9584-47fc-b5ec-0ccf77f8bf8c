import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_shaders/flutter_shaders.dart';
import 'package:hopen/presentation/providers/shader_provider.dart';

/// A widget that applies RGB split distortion shader effect to its child
///
/// This widget wraps any child widget and applies a dynamic RGB distortion
/// effect based on user interaction. The effect is purely visual and doesn't
/// affect the child widget's functionality.
class RgbShaderEffect extends StatefulWidget {
  const RgbShaderEffect({
    super.key,
    required this.child,
    this.enabled = true,
    this.velocityMultiplier = 40.0,
    this.falloffDistance = 190.0,
    this.intensity = 0.5,
  });

  /// The child widget to apply the shader effect to
  final Widget child;

  /// Whether the shader effect is enabled
  final bool enabled;

  /// Multiplier for velocity sensitivity (higher = more sensitive)
  final double velocityMultiplier;

  /// Distance for effect falloff (lower = more localized effect)
  final double falloffDistance;

  /// Intensity of the RGB distortion effect (0.0 to 1.0)
  final double intensity;

  @override
  State<RgbShaderEffect> createState() => _RgbShaderEffectState();
}

class _RgbShaderEffectState extends State<RgbShaderEffect>
    with SingleTickerProviderStateMixin {
  Offset _position = Offset.zero;
  Offset _desiredPosition = Offset.zero;
  Offset _velocity = Offset.zero;
  late final Ticker _ticker;
  Duration _lastTime = Duration.zero;
  double _time = 0.0;

  @override
  void initState() {
    super.initState();
    _ticker = createTicker(_onUpdate)..start();
  }

  @override
  void dispose() {
    _ticker.stop();
    _ticker.dispose();
    super.dispose();
  }

  void _onUpdate(Duration elapsed) {
    final delta =
        ((elapsed.inMicroseconds - _lastTime.inMicroseconds) /
            Duration.microsecondsPerSecond) *
        60;

    _lastTime = elapsed;
    _time = elapsed.inMilliseconds / 1000.0; // Convert to seconds

    if (_desiredPosition == _position) {
      return;
    }

    setState(() {
      final distance = _desiredPosition - _position;
      final amplitude = 1 - max(0, 1000 - distance.distance) / 1000;
      _velocity =
          distance * (0.02 + 0.2 * Curves.easeInOut.transform(amplitude));
      _position += _velocity * delta;
    });
  }

  void _onPanStart(DragStartDetails details) {
    setState(() {
      _desiredPosition = details.globalPosition;
      _position = details.globalPosition;
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    setState(() {
      _desiredPosition = details.globalPosition;
    });
  }

  void _onPanEnd(DragEndDetails details) {
    // Keep the current state, let the animation naturally settle
  }

  @override
  Widget build(BuildContext context) {
    // Check for reduced motion accessibility preference
    final reduceMotion = MediaQuery.of(context).accessibleNavigation;

    // If shader is disabled or motion should be reduced, return child directly
    if (!widget.enabled || reduceMotion) {
      return widget.child;
    }

    // Try to get shader collection, fallback to child if not available
    final shaderCollection = ShaderProvider.maybeOf(context);
    if (shaderCollection == null) {
      return widget.child;
    }

    return RepaintBoundary(
      child: GestureDetector(
        onPanStart: _onPanStart,
        onPanUpdate: _onPanUpdate,
        onPanEnd: _onPanEnd,
        onPanCancel: () {
          // Reset to neutral state on cancel
          setState(() {
            _velocity = Offset.zero;
          });
        },
        child: AnimatedSampler((image, size, canvas) {
          try {
            final shader = shaderCollection.rgbShader.fragmentShader();

            // Set shader uniforms
            shader
              ..setFloat(0, size.width) // u_size.x
              ..setFloat(1, size.height) // u_size.y
              ..setFloat(2, _desiredPosition.dx) // u_location.x
              ..setFloat(3, _desiredPosition.dy) // u_location.y
              ..setFloat(
                4,
                _velocity.dx * widget.velocityMultiplier,
              ) // u_velocity.x
              ..setFloat(
                5,
                _velocity.dy * widget.velocityMultiplier,
              ) // u_velocity.y
              ..setFloat(6, _time) // u_time
              ..setFloat(7, widget.intensity) // u_intensity
              ..setImageSampler(0, image); // u_texture

            // Draw the shader effect
            canvas.drawRect(
              Rect.fromLTWH(0, 0, size.width, size.height),
              Paint()..shader = shader,
            );
          } catch (e) {
            print('❌ Error applying RGB shader: $e');
            // Fallback: draw the original image without shader
            canvas.drawImage(image, Offset.zero, Paint());
          }
        }, child: widget.child),
      ),
    );
  }
}
