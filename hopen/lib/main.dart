import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import 'config/app_config.dart'; // Add AppConfig import
import 'di/injection_container_refactored.dart' as di;
import 'provider/services/auth/ory_auth_service.dart';
import 'provider/services/call/platform_call_handler.dart';

import 'provider/services/notification_service_fcm.dart';
import 'provider/services/real_time_service_manager.dart';
import 'provider/services/performance/startup_performance_service.dart';
import 'provider/services/app_context_manager.dart';
import 'provider/theme/theme_provider.dart';
import 'statefulbusinesslogic/core/notifiers/user_profile_notifier_interface.dart';
import 'statefulbusinesslogic/bloc/active_bubble/active_bubble_bloc.dart';
import 'statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import 'statefulbusinesslogic/bloc/auth/auth_event.dart';
import 'statefulbusinesslogic/bloc/auth/auth_state.dart';
import 'provider/services/user_profile_preloader_service.dart';

import 'statefulbusinesslogic/bloc/bubble/bubble_bloc.dart';
import 'statefulbusinesslogic/bloc/bubble_history/bubble_history_bloc.dart';
import 'statefulbusinesslogic/bloc/bubble_invite_request/bubble_invite_request_bloc.dart';
import 'statefulbusinesslogic/bloc/bubble_join_request/bubble_join_request_bloc.dart';
import 'statefulbusinesslogic/bloc/call/call_bloc.dart';
import 'statefulbusinesslogic/bloc/chat/chat_bloc.dart';
import 'statefulbusinesslogic/bloc/contact_request/contact_request_bloc.dart';
import 'statefulbusinesslogic/bloc/friend_request/friend_request_bloc.dart';
import 'statefulbusinesslogic/bloc/contacts/contacts_bloc.dart';
import 'statefulbusinesslogic/bloc/friend_selection/friend_selection_bloc.dart';
import 'statefulbusinesslogic/bloc/friends/friends_bloc.dart';
import 'statefulbusinesslogic/bloc/notification/notification_bloc.dart';
import 'statefulbusinesslogic/bloc/signup/signup_bloc.dart';
import 'statefulbusinesslogic/bloc/theme/theme_bloc.dart';
import 'statefulbusinesslogic/bloc/activity_status/activity_status_bloc.dart';
import 'statefulbusinesslogic/core/services/activity_status_service.dart';
import 'statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import 'statefulbusinesslogic/core/services/logging_service.dart';
import 'statefulbusinesslogic/bloc/bubble/bubble_event.dart';
import 'statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import 'statefulbusinesslogic/bloc/user_profile/user_profile_bloc.dart';
import 'presentation/widgets/connectivity_toast_listener.dart';
import 'presentation/widgets/requests/friends_choice_dialog.dart';
import 'presentation/widgets/requests/contact_request_dialog.dart';
import 'presentation/providers/shader_provider.dart';
// friend_request_dialog.dart removed - friendships are auto-generated at bubble expiry
import 'presentation/widgets/requests/bubble_start_request_dialog.dart';
import 'presentation/widgets/requests/bubble_invite_request_dialog.dart';
import 'presentation/widgets/requests/bubble_join_request_dialog.dart';
import 'presentation/widgets/requests/bubble_kickout_request_dialog.dart';
import 'presentation/widgets/requests/bubble_propose_request_dialog.dart';

import 'statefulbusinesslogic/bloc/notification/notification_event.dart';
import 'statefulbusinesslogic/core/services/notification_orchestrator.dart';
import 'statefulbusinesslogic/core/models/bubble_member.dart';
import 'statefulbusinesslogic/core/models/user_model.dart';

import 'presentation/router/app_router.dart' as app_router;

// Global error display key for catching unhandled errors
final GlobalKey<ScaffoldMessengerState> rootScaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

// Assume 'router' is your GoRouter instance, configured with navigatorKey
// This might be initialized in a separate file and imported, or here directly.
// For this example, we will assume 'router' is passed to MyApp after being configured.

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Force portrait orientation globally by default.
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // Print configuration for debugging
  AppConfig.printConfig();

  // Set up global error handler before any async operations
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    LoggingService.error(
      'Flutter error: ${details.exception}',
      error: details.exception,
      stackTrace: details.stack,
    );

    // Show a visible error indicator on the app after the current frame
    SchedulerBinding.instance.addPostFrameCallback((_) {
      try {
        if (rootScaffoldMessengerKey.currentState?.mounted == true) {
          rootScaffoldMessengerKey.currentState?.showSnackBar(
            SnackBar(
              content: Text('Error: ${details.exception}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 10),
            ),
          );
        }
      } catch (e) {
        LoggingService.warning('Failed to show error snackbar: $e');
      }
    });
  };

  // ✅ ENABLED: Ory Stack authentication initialization
  try {
    await OryAuthService().initialize();
    LoggingService.success('Ory Stack authentication initialized successfully');
    LoggingService.info('   Kratos URL: ${AppConfig.oryKratosPublicUrl}');
    LoggingService.info('   Hydra URL: ${AppConfig.oryHydraPublicUrl}');
  } catch (e, s) {
    LoggingService.failure(
      'FAILED to initialize Ory Stack',
      error: e,
      stackTrace: s,
    );
    // Continue without Ory Stack if it fails
  }

  try {
    // Initialize Firebase with platform-specific options
    if (kIsWeb) {
      // Web configuration - using Firebase project ID from AppConfig
      await Firebase.initializeApp(
        options: const FirebaseOptions(
          apiKey:
              'AIzaSyBqJVJKQQQQQQQQQQQQQQQQQQQQQQQQQQQ', // Replace with your actual web API key
          authDomain: '${AppConfig.firebaseProjectId}.firebaseapp.com',
          projectId: AppConfig.firebaseProjectId,
          storageBucket: '${AppConfig.firebaseProjectId}.appspot.com',
          messagingSenderId: '257996495540', // Your messaging sender ID
          appId:
              '1:257996495540:web:your-web-app-id', // Replace with your actual web app ID
        ),
      );
    } else {
      // Mobile platforms - first check if configuration exists
      try {
        await Firebase.initializeApp();
        LoggingService.success(
          'Firebase initialized successfully from default config',
        );
      } catch (configError) {
        LoggingService.warning(
          'Failed to initialize Firebase from default config',
          error: configError,
        );
        LoggingService.info(
          'Skipping Firebase initialization - app will continue without Firebase features',
          tag: 'Firebase',
        );
      }
    }
  } catch (e, s) {
    LoggingService.failure(
      'FAILED to initialize Firebase',
      error: e,
      stackTrace: s,
    );
    LoggingService.info('App will continue without Firebase features');
  }

  try {
    // Initialize dependency injection (performance monitoring starts here)
    await di.init();
    LoggingService.success('Dependency injection initialized successfully');

    // Profile startup phases
    final performanceService = di.sl.get<StartupPerformanceService>();
    performanceService.completeOperation('dependency_injection');

    // Wait for all dependencies to be ready
    await di.sl.allReady();
    LoggingService.success('All dependencies are ready');

    // Initialize ActivityStatusService
    try {
      LoggingService.info('Initializing ActivityStatusService...');
      final activityStatusService = di.sl<ActivityStatusService>();
      await activityStatusService.initialize();
      LoggingService.success('ActivityStatusService initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.failure(
        'FAILED TO INITIALIZE ActivityStatusService in main()',
        error: e,
        stackTrace: stackTrace,
        tag: 'ActivityStatus',
      );
      LoggingService.warning(
        'Activity status functionality will not work properly',
      );
    }
  } catch (e, s) {
    LoggingService.failure(
      'FAILED to initialize dependency injection',
      error: e,
      stackTrace: s,
    );
    rethrow; // Rethrow to prevent app from starting with incomplete initialization
  }

  // IncomingCallListener functionality is now integrated into RealTimeNotificationService
  if (kDebugMode) {
    LoggingService.success(
      'Call notifications are handled by RealTimeNotificationService',
    );
  }

  // Initialize platform call handler for native incoming calls
  if (!kIsWeb) {
    try {
      LoggingService.info(
        'Initializing PlatformCallHandler for native call handling...',
      );
      final platformCallHandler = PlatformCallHandler();
      await platformCallHandler.initialize();
      // Register in DI for potential future access
      di.sl.registerSingleton<PlatformCallHandler>(platformCallHandler);
      LoggingService.success('PlatformCallHandler initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.failure(
        'FAILED TO INITIALIZE PlatformCallHandler in main()',
        error: e,
        stackTrace: stackTrace,
        tag: 'PlatformCalls',
      );
      LoggingService.warning('Background call handling will not work properly');
    }
  }

  // Initialize notification service (ensure it handles non-web gracefully if needed)
  if (!kIsWeb) {
    try {
      LoggingService.info('Attempting to initialize NotificationService...');
      // Only initialize notifications for mobile (or add web support)
      await NotificationServiceFCM().initialize();
      LoggingService.success(
        'NotificationService initialized successfully (non-web)',
      );
    } catch (e, stackTrace) {
      LoggingService.failure(
        'FAILED TO INITIALIZE NotificationService in main()',
        error: e,
        stackTrace: stackTrace,
        tag: 'Notifications',
      );
    }
  }

  // Initialize unified real-time service manager
  try {
    LoggingService.info('Initializing Unified RealTimeServiceManager...');
    final realTimeServiceManager = di.sl<RealTimeServiceManager>();
    await realTimeServiceManager.initialize();
    LoggingService.success(
      'Unified RealTimeServiceManager initialized successfully',
    );
  } catch (e, stackTrace) {
    LoggingService.failure(
      'FAILED TO INITIALIZE Unified RealTimeServiceManager in main()',
      error: e,
      stackTrace: stackTrace,
      tag: 'RealTimeServices',
    );
    LoggingService.warning('Real-time notifications will not work properly');
  }

  // Contact request notifications are now handled by the unified RealTimeNotificationService
  // which is initialized automatically when users log in

  // Configure system UI for edge-to-edge display
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  // Enable edge-to-edge display (extend UI under system navigation bars)
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  // Mark startup as complete before launching the app
  try {
    final performanceService = di.sl<StartupPerformanceService>();
    performanceService.markStartupComplete();
  } catch (e) {
    LoggingService.warning('Failed to mark startup complete', error: e);
  }

  // final router = AppRouter(navigatorKey: navigatorKey).router; // If AppRouter structure is used
  try {
    final router =
        di
            .sl<
              GoRouter
            >(); // Assuming router is in DI and configured with the key
    print('Router fetched successfully from DI');

    // Load shaders
    print('🎨 Loading shaders...');
    final shaderCollection = await ShaderService.loadShaders();
    print('✅ Shaders loaded successfully');

    runApp(
      ShaderProvider(
        shaderCollection: shaderCollection,
        child: MyApp(router: router),
      ),
    );
  } catch (e, s) {
    print('FAILED to get router from DI or load shaders: $e');
    print(s);

    // Run a simplified error app instead
    runApp(ErrorApp(error: e.toString(), stackTrace: s.toString()));
  }
}

// Simple error display app as a fallback when the main app fails to initialize
class ErrorApp extends StatelessWidget {
  const ErrorApp({required this.error, required this.stackTrace, super.key});
  final String error;
  final String stackTrace;

  @override
  Widget build(BuildContext context) => MaterialApp(
    title: 'Hopen Error',
    theme: ThemeData(
      primarySwatch: Colors.blue,
      scaffoldBackgroundColor: const Color(0xFF0A2955),
    ),
    home: Scaffold(
      appBar: AppBar(title: const Text('Hopen Initialization Error')),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'The app encountered an error during initialization:',
                style: TextStyle(fontSize: 18, color: Colors.white),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  error,
                  style: const TextStyle(fontSize: 16, color: Colors.white),
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Stack Trace:',
                style: TextStyle(fontSize: 16, color: Colors.white70),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      stackTrace,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({required this.router, super.key});
  final GoRouter router;

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late StreamSubscription<DialogEvent> _dialogEventSubscription;
  late NotificationOrchestrator _notificationOrchestrator;
  final Set<String> _shownDialogs =
      <String>{}; // Track shown dialogs to prevent duplicates

  @override
  void initState() {
    super.initState();
    _notificationOrchestrator = di.sl<NotificationOrchestrator>();
    _dialogEventSubscription = _notificationOrchestrator.dialogEventStream
        .listen(_handleDialogEvent);

    // Check for existing pending requests after a longer delay to ensure MaterialApp is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 5), () {
        _checkForExistingPendingRequests();
      });
    });
  }

  @override
  void dispose() {
    _dialogEventSubscription.cancel();
    super.dispose();
  }

  /// Check for existing pending requests using the new sync system
  Future<void> _checkForExistingPendingRequests() async {
    try {
      print(
        '🔔 MyApp: Using new sync system - pending requests handled by MQTT events',
      );

      // The new sync system works differently:
      // 1. Initial sync fetches all data including pending requests
      // 2. Data is stored in local database
      // 3. MQTT events trigger dialogs for pending requests
      // 4. No need to manually check for requests here

      print(
        '🔔 MyApp: Pending requests will be handled automatically by MQTT event system',
      );
    } catch (e) {
      print('🔔 MyApp: Error in sync system check: $e');
    }
  }

  /// Handle dialog events from NotificationOrchestrator
  void _handleDialogEvent(DialogEvent event) {
    print('🔔 MyApp: Received dialog event: ${event.type}');

    WidgetsBinding.instance.addPostFrameCallback((_) {
      switch (event.type) {
        case 'contact_request_received':
          _showContactRequestDialog(context, event.data);
          break;
        case 'bubble_start_request_received':
          _showBubbleStartRequestDialog(context, event.data);
          break;
        case 'bubble_invite_request_received':
          _showBubbleInviteRequestDialog(context, event.data);
          break;
        case 'bubble_join_request_received':
          _showBubbleJoinRequestDialog(context, event.data);
          break;
        case 'bubble_kickout_request_received':
          _showBubbleKickoutRequestDialog(context, event.data);
          break;
        case 'bubble_propose_request_received':
          _showBubbleProposeRequestDialog(context, event.data);
          break;
        case 'friend_request_received':
          // Friend requests are auto-generated at bubble expiry - no manual dialog needed
          // Users receive auto-generated requests and accept/decline through the friends system
          break;
        case 'bubble_expired':
          _showFriendsChoiceDialog(context, event.data);
          break;
        default:
          print('🔔 MyApp: Unknown dialog event type: ${event.type}');
      }
    });
  }

  /// Show contact request dialog when notification is received
  void _showContactRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) {
    final requestId = data['requestId'] ?? data['id'] ?? 'temp_request_id';
    final requesterId =
        data['fromUserId'] ?? data['requester_id'] ?? data['senderId'] ?? '';
    final requesterName =
        data['fromUserName'] ??
        data['requester_name'] ??
        data['senderName'] ??
        'Unknown User';
    final requesterUsername =
        data['requesterUsername'] ??
        data['requester_username'] ??
        data['senderUsername'];
    final requesterProfilePicUrl =
        data['requesterProfilePicUrl'] ??
        data['requester_profile_pic_url'] ??
        data['senderProfilePicUrl'];
    final requestTimestamp =
        DateTime.tryParse(
          data['timestamp']?.toString() ?? data['sentAt']?.toString() ?? '',
        ) ??
        DateTime.now();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'contact_request_$requestId';

    // Check if this dialog has already been shown
    if (_shownDialogs.contains(dialogKey)) {
      print(
        '🔔 MyApp: Contact request dialog for $requesterName already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    _shownDialogs.add(dialogKey);
    print(
      '🔔 MyApp: Showing contact request dialog for $requesterName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null) {
      print('🔔 MyApp: Navigator context not available, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      _shownDialogs.remove(dialogKey);
      return;
    }

    showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      useRootNavigator: true,
      builder: (BuildContext dialogContext) {
        return ContactRequestDialog(
          requestId: requestId.toString(),
          requesterId: requesterId.toString(),
          requesterName: requesterName.toString(),
          requesterUsername: requesterUsername?.toString(),
          requesterProfilePicUrl: requesterProfilePicUrl?.toString(),
          requestTimestamp: requestTimestamp,
        );
      },
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      _shownDialogs.remove(dialogKey);
      print(
        '🔔 MyApp: Contact request dialog dismissed, removed key: $dialogKey',
      );
    });
  }

  /// Show bubble start request dialog
  void _showBubbleStartRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) {
    final requestId =
        data['requestId'] ?? data['id'] ?? 'temp_bubble_start_request';
    final requesterId = data['fromUserId'] ?? data['requester_id'] ?? '';
    final requesterName = data['fromUserName'] ?? data['requester_name'] ?? '';
    final requesterUsername =
        data['requesterUsername'] ?? data['requester_username'];
    final requesterProfilePicUrl =
        data['requesterProfilePicUrl'] ?? data['requester_profile_pic_url'];
    final requestTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_start_request_$requestId';

    // Check if this dialog has already been shown
    if (_shownDialogs.contains(dialogKey)) {
      print(
        '🔔 MyApp: Bubble start request dialog for $requesterName already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    _shownDialogs.add(dialogKey);
    print(
      '🔔 MyApp: Showing bubble start request dialog for $requesterName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null) {
      print('🔔 MyApp: Navigator context not available, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      _shownDialogs.remove(dialogKey);
      return;
    }

    showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      useRootNavigator: true,
      builder: (BuildContext dialogContext) {
        return BubbleStartRequestDialog(
          requesterId: requesterId.toString(),
          requesterName: requesterName.toString(),
          requesterUsername: requesterUsername?.toString(),
          requesterProfilePicUrl: requesterProfilePicUrl?.toString(),
          requestTimestamp: requestTimestamp,
        );
      },
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      _shownDialogs.remove(dialogKey);
      print(
        '🔔 MyApp: Bubble start request dialog dismissed, removed key: $dialogKey',
      );
    });
  }

  /// Show bubble invite request dialog
  void _showBubbleInviteRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) {
    final requestId =
        data['requestId'] ?? data['id'] ?? 'temp_bubble_invite_request';
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';
    final inviterName = data['fromUserName'] ?? data['inviter_name'] ?? '';
    final inviterId = data['fromUserId'] ?? data['inviter_id'] ?? '';
    final inviterProfilePicUrl =
        data['inviterProfilePicUrl'] ?? data['inviter_profile_pic_url'];
    final inviteTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_invite_request_$requestId';

    // Check if this dialog has already been shown
    if (_shownDialogs.contains(dialogKey)) {
      print(
        '🔔 MyApp: Bubble invite request dialog for $inviterName already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    _shownDialogs.add(dialogKey);

    // Create a list of bubble members (for now, just the inviter)
    final members = <BubbleMember>[
      BubbleMember(
        id: 'member_${inviterId}',
        bubbleId: bubbleId.toString(),
        userId: inviterId.toString(),
        joinedAt: inviteTimestamp,
        name: inviterName.toString(),
        profilePicUrl: inviterProfilePicUrl?.toString(),
        inviteTimestamp: inviteTimestamp,
      ),
    ];

    print(
      '🔔 MyApp: Showing bubble invite request dialog for $inviterName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null) {
      print('🔔 MyApp: Navigator context not available, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      _shownDialogs.remove(dialogKey);
      return;
    }

    showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      useRootNavigator: true,
      builder: (BuildContext dialogContext) {
        return BubbleInviteRequestDialog(
          bubbleId: bubbleId.toString(),
          bubbleName: bubbleName.toString(),
          members: members,
        );
      },
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      _shownDialogs.remove(dialogKey);
      print(
        '🔔 MyApp: Bubble invite request dialog dismissed, removed key: $dialogKey',
      );
    });
  }

  /// Show bubble join request dialog
  void _showBubbleJoinRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) {
    final requestId =
        data['requestId'] ?? data['request_id'] ?? 'temp_request_id';
    final requesterId = data['fromUserId'] ?? data['requester_id'] ?? '';
    final requesterName = data['fromUserName'] ?? data['requester_name'] ?? '';
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';
    final requesterProfilePicUrl =
        data['requesterProfilePicUrl'] ?? data['requester_profile_pic_url'];
    final requestTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();
    final message = data['message']?.toString();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_join_request_$requestId';

    // Check if this dialog has already been shown
    if (_shownDialogs.contains(dialogKey)) {
      print(
        '🔔 MyApp: Bubble join request dialog for $requesterName already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    _shownDialogs.add(dialogKey);

    // Create a list of bubble members (for now, just the requester)
    final members = <BubbleMember>[
      BubbleMember(
        id: 'member_${requesterId}',
        bubbleId: bubbleId.toString(),
        userId: requesterId.toString(),
        joinedAt: requestTimestamp,
        name: requesterName.toString(),
        profilePicUrl: requesterProfilePicUrl?.toString(),
      ),
    ];

    print(
      '🔔 MyApp: Showing bubble join request dialog for $requesterName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null) {
      print('🔔 MyApp: Navigator context not available, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      _shownDialogs.remove(dialogKey);
      return;
    }

    showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      useRootNavigator: true,
      builder: (BuildContext dialogContext) {
        return BubbleJoinRequestDialog(
          requestId: requestId.toString(),
          requesterId: requesterId.toString(),
          requesterName: requesterName.toString(),
          bubbleId: bubbleId.toString(),
          bubbleName: bubbleName.toString(),
          members: members,
          message: message,
        );
      },
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      _shownDialogs.remove(dialogKey);
      print(
        '🔔 MyApp: Bubble join request dialog dismissed, removed key: $dialogKey',
      );
    });
  }

  /// Show bubble kickout request dialog
  void _showBubbleKickoutRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) {
    final requestId =
        data['requestId'] ?? data['request_id'] ?? 'temp_request_id';
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';
    final targetMemberId =
        data['targetMemberId'] ?? data['target_member_id'] ?? '';
    final targetMemberName =
        data['targetMemberName'] ?? data['target_member_name'] ?? '';
    final targetMemberProfilePicUrl =
        data['targetMemberProfilePicUrl'] ??
        data['target_member_profile_pic_url'];
    final requesterId = data['fromUserId'] ?? data['requester_id'] ?? '';
    final requesterName = data['fromUserName'] ?? data['requester_name'] ?? '';
    final requestTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_kickout_request_$requestId';

    // Check if this dialog has already been shown
    if (_shownDialogs.contains(dialogKey)) {
      print(
        '🔔 MyApp: Bubble kickout request dialog for $targetMemberName already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    _shownDialogs.add(dialogKey);

    // Create a list of bubble members (target member and requester)
    final members = <BubbleMember>[
      BubbleMember(
        id: 'member_${targetMemberId}',
        bubbleId: bubbleId.toString(),
        userId: targetMemberId.toString(),
        joinedAt: requestTimestamp,
        name: targetMemberName.toString(),
        profilePicUrl: targetMemberProfilePicUrl?.toString(),
      ),
      BubbleMember(
        id: 'member_${requesterId}',
        bubbleId: bubbleId.toString(),
        userId: requesterId.toString(),
        joinedAt: requestTimestamp,
        name: requesterName.toString(),
      ),
    ];

    print(
      '🔔 MyApp: Showing bubble kickout request dialog for $targetMemberName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null) {
      print('🔔 MyApp: Navigator context not available, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      _shownDialogs.remove(dialogKey);
      return;
    }

    showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      useRootNavigator: true,
      builder: (BuildContext dialogContext) {
        return BubbleKickoutRequestDialog(
          requestId: requestId.toString(),
          bubbleName: bubbleName.toString(),
          members: members,
        );
      },
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      _shownDialogs.remove(dialogKey);
      print(
        '🔔 MyApp: Bubble kickout request dialog dismissed, removed key: $dialogKey',
      );
    });
  }

  /// Show bubble propose request dialog
  void _showBubbleProposeRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) {
    final requestId =
        data['requestId'] ?? data['id'] ?? 'temp_bubble_propose_request';
    final proposedMemberId =
        data['proposedMemberId'] ?? data['proposed_member_id'] ?? '';
    final proposedMemberName =
        data['proposedMemberName'] ?? data['proposed_member_name'] ?? '';
    final proposerId = data['fromUserId'] ?? data['proposer_id'] ?? '';
    final proposerName = data['fromUserName'] ?? data['proposer_name'] ?? '';
    final proposedMemberProfilePicUrl =
        data['proposedMemberProfilePicUrl'] ??
        data['proposed_member_profile_pic_url'];
    final proposeTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_propose_request_$requestId';

    // Check if this dialog has already been shown
    if (_shownDialogs.contains(dialogKey)) {
      print(
        '🔔 MyApp: Bubble propose request dialog for $proposedMemberName already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    _shownDialogs.add(dialogKey);

    // Create a list of bubble members (proposed member and proposer)
    final members = <BubbleMember>[
      BubbleMember(
        id: 'member_${proposedMemberId}',
        bubbleId: bubbleId.toString(),
        userId: proposedMemberId.toString(),
        joinedAt: proposeTimestamp,
        name: proposedMemberName.toString(),
        profilePicUrl: proposedMemberProfilePicUrl?.toString(),
        proposeTimestamp: proposeTimestamp,
      ),
      BubbleMember(
        id: 'member_${proposerId}',
        bubbleId: bubbleId.toString(),
        userId: proposerId.toString(),
        joinedAt: proposeTimestamp,
        name: proposerName.toString(),
      ),
    ];

    print(
      '🔔 MyApp: Showing bubble propose request dialog for $proposedMemberName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null) {
      print('🔔 MyApp: Navigator context not available, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      _shownDialogs.remove(dialogKey);
      return;
    }

    showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      useRootNavigator: true,
      builder: (BuildContext dialogContext) {
        return BubbleProposeRequestDialog(
          bubbleId: bubbleId.toString(),
          bubbleName: bubbleName.toString(),
          members: members,
          proposedMemberId: proposedMemberId.toString(),
        );
      },
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      _shownDialogs.remove(dialogKey);
      print(
        '🔔 MyApp: Bubble propose request dialog dismissed, removed key: $dialogKey',
      );
    });
  }

  // _showFriendRequestDialog removed - friend requests are auto-generated at bubble expiry
  // Users receive auto-generated friend requests and handle them through the friends system

  /// Show friends choice dialog when bubble expires
  void _showFriendsChoiceDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) {
    final requestId =
        data['requestId'] ?? data['id'] ?? 'temp_friends_choice_request';
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';
    final formerMembersData =
        data['formerMembers'] as List<Map<String, dynamic>>? ?? [];

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'friends_choice_$requestId';

    // Check if this dialog has already been shown
    if (_shownDialogs.contains(dialogKey)) {
      print(
        '🔔 MyApp: Friends choice dialog for bubble $bubbleName already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    _shownDialogs.add(dialogKey);

    // Convert former members data to UserModel list
    final formerMembers =
        formerMembersData.map((memberData) {
          return UserModel(
            id: memberData['id']?.toString() ?? '',
            firstName:
                memberData['firstName']?.toString() ??
                memberData['first_name']?.toString(),
            lastName:
                memberData['lastName']?.toString() ??
                memberData['last_name']?.toString(),
            username: memberData['username']?.toString(),
            profilePictureUrl:
                memberData['profilePictureUrl']?.toString() ??
                memberData['profile_pic_url']?.toString(),
            email: memberData['email']?.toString(),
          );
        }).toList();

    print(
      '🔔 MyApp: Showing friends choice dialog for bubble: $bubbleName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null) {
      print('🔔 MyApp: Navigator context not available, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      _shownDialogs.remove(dialogKey);
      return;
    }

    showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      useRootNavigator: true,
      builder: (BuildContext dialogContext) {
        return FriendsChoiceDialog(
          bubbleId: bubbleId.toString(),
          bubbleName: bubbleName.toString(),
          formerMembers: formerMembers,
        );
      },
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      _shownDialogs.remove(dialogKey);
      print(
        '🔔 MyApp: Friends choice dialog dismissed, removed key: $dialogKey',
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    // The BLoC provider for authentication will wrap the entire application
    // to make authentication state globally accessible.
    return MultiProvider(
      providers: [
        BlocProvider<AuthBloc>(
          create: (_) => di.sl<AuthBloc>()..add(const CheckAuthStatusEvent()),
        ),
        BlocProvider<SignUpBloc>(create: (_) => di.sl<SignUpBloc>()),
        BlocProvider<NotificationBloc>(
          create:
              (_) => di.sl<NotificationBloc>()..add(const FetchNotifications()),
        ),
        BlocProvider<BubbleHistoryBloc>(
          create: (_) => di.sl<BubbleHistoryBloc>(),
        ),
        BlocProvider<ContactsBloc>(create: (_) => di.sl<ContactsBloc>()),
        BlocProvider<ContactRequestBloc>(
          create: (_) => di.sl<ContactRequestBloc>(),
        ),
        BlocProvider<BubbleBloc>(
          create: (_) => di.sl<BubbleBloc>()..add(const LoadBubble()),
        ),
        BlocProvider<FriendsBloc>(create: (_) => di.sl<FriendsBloc>()),
        BlocProvider<UnifiedProfileBloc>(
          create: (_) => di.sl<UnifiedProfileBloc>(),
        ),
        BlocProvider<UserProfileBloc>(create: (_) => di.sl<UserProfileBloc>()),
        BlocProvider<CallBloc>(create: (_) => di.sl<CallBloc>()),
        BlocProvider<ActiveBubbleBloc>(
          create: (_) => di.sl<ActiveBubbleBloc>(),
        ),
        BlocProvider<ChatBloc>(create: (_) => di.sl<ChatBloc>()),
        BlocProvider<FriendSelectionBloc>(
          create: (_) => di.sl<FriendSelectionBloc>(),
        ),
        BlocProvider<FriendRequestBloc>(
          create: (_) => di.sl<FriendRequestBloc>(),
        ),
        BlocProvider<BubbleJoinRequestBloc>(
          create: (_) => di.sl<BubbleJoinRequestBloc>(),
        ),
        BlocProvider<BubbleInviteRequestBloc>(
          create: (_) => di.sl<BubbleInviteRequestBloc>(),
        ),
        BlocProvider<ThemeBloc>(create: (_) => di.sl<ThemeBloc>()),
        BlocProvider<ActivityStatusBloc>(
          create: (_) => di.sl<ActivityStatusBloc>(),
        ),
        // Note: BubbleProposeRequestBloc, BubbleCountdownBloc, and VoiceRecordingBloc
        // are not registered in DI container yet
        ChangeNotifierProvider<NavBarVisibilityNotifier>(
          create: (_) => NavBarVisibilityNotifier(),
        ),
        ChangeNotifierProvider<ThemeProvider>(
          create: (_) => di.sl<ThemeProvider>(),
        ),
        // User Profile Provider (Stale-While-Revalidate caching)
        ChangeNotifierProvider<UserProfileNotifierInterface>(
          create: (_) => di.sl<UserProfileNotifierInterface>(),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder:
            (
              context,
              themeProvider,
              child,
            ) => BlocListener<AuthBloc, AuthState>(
              listenWhen:
                  (previous, current) =>
                      previous.status != current.status &&
                      current.status == AuthStatus.authenticated,
              listener: (context, state) {
                // Trigger user profile preloading immediately when user becomes authenticated
                if (state.userId != null) {
                  print(
                    '👤 MyApp: User authenticated, triggering profile preload for ${state.userId}',
                  );
                  try {
                    final preloader = di.sl<UserProfilePreloaderService>();
                    preloader.preloadUserDataById(state.userId!);
                  } catch (e) {
                    print('👤 MyApp: Error triggering profile preload: $e');
                  }
                }
              },
              child: MaterialApp.router(
                scaffoldMessengerKey: rootScaffoldMessengerKey,
                title: 'Hopen',
                theme: themeProvider.getTheme(context),
                routerConfig: widget.router,
                debugShowCheckedModeBanner: false,
                localizationsDelegates: const [
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: const [Locale('en', 'US')],
                builder: (context, child) {
                  print(
                    'MaterialApp.router builder called - creating AppContextProvider',
                  );
                  return AppContextProvider(
                    child: NavigatorContextProvider(
                      child: ConnectivityToastListener(
                        child: child ?? const SizedBox.shrink(),
                      ),
                    ),
                  );
                },
              ),
            ),
      ),
    );
  }
}

// Remove the default MyHomePage and related code below this line
